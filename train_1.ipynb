import random
import threading
from collections import defaultdict
from itertools import combinations, product
from pathlib import Path
from typing import Dict, Optional, Tu<PERSON>
from pytorch_metric_learning import miners, losses

import numpy as np
import pandas as pd
import torch
import torchaudio

from sklearn.metrics import (
    confusion_matrix,
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    classification_report
)

from tqdm.notebook import tqdm
from torch import nn, optim
from torch.nn import functional as F
from torch.nn.utils.rnn import pad_sequence
from torch.optim.lr_scheduler import ReduceLROnPlateau
from torch.utils.data import DataLoader, Dataset

# SpeechBrain ECAPA-TDNN
from speechbrain.inference.speaker import SpeakerRecognition
from speechbrain.inference.speaker import EncoderClassifier

CONFIG = {
    "global": {
        "sample_rate": 16000
    },
    "train": {
        "batch_size": 6,
        "opt_lr": 1e-3,
        "opt_weight_decay": 1e-5,
        "backbone_lr": 1e-5,
        "loss_margin": 0.3,
        "num_epochs": 10,
        "lr_scheduler": {
            "factor": 0.1,
            "patience": 2,
            "threshold": 1e-6,
            "threshold_mode": "rel",
        }
    }
}

# Filepaths
BASE_PATH = Path("..")

ORI_DATASETPATH = BASE_PATH / "4DigitsDataset"
GEN_DATASETPATH = [
    BASE_PATH / "4DigitsDatasetGosyVoice",
    BASE_PATH / "4DigitsDatasetF5TTS",
]

METADATA_PATH = {
    "sv_in_train": Path("./dataset_meta_train.csv"),
    "sv_in_test": Path("./dataset_meta_test.csv"),
    "sv_out": Path("./dataset_meta_cross.csv"),
    "dd_in_train": Path("./dataset_meta_train_dd.csv"),
    "dd_in_test": Path("./dataset_meta_test_dd.csv"),
    "dd_out": Path("./dataset_meta_cross_dd.csv"),
}

CHECKPOINT_PATH = Path("./checkpoints")
if not CHECKPOINT_PATH.exists():
    CHECKPOINT_PATH.mkdir(parents=True)

print(f"""
Existence Check:

DATASET:
REAL: {ORI_DATASETPATH.exists()}
FAKE: {[path.exists() for path in GEN_DATASETPATH]}

METADATA:
{[f"{k}: {v.exists()}" for k, v in METADATA_PATH.items()]}

CHECKPOINT:
{CHECKPOINT_PATH.exists()}
""")

speaker2idx = {spk.stem: i for i, spk in enumerate(ORI_DATASETPATH.iterdir())}
print(f"{len(speaker2idx)} speakers are available for training.")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

classifier = EncoderClassifier.from_hparams(source="speechbrain/spkrec-ecapa-voxceleb",
                                            savedir="pretrained_models/spkrec-ecapa-voxceleb",
                                            run_opts={"device": device.type})
verification = SpeakerRecognition.from_hparams(source="speechbrain/spkrec-ecapa-voxceleb",
                                               savedir="pretrained_models/spkrec-ecapa-voxceleb",
                                               run_opts={"device": device.type})

def load_data(metadata_path: Path):
    df = pd.read_csv(metadata_path)
    real_lst = df['real'].tolist()
    fake1_lst, fake2_lst = df['fake1'].tolist(), df['fake2'].tolist()
    same_lst = df['same'].tolist()
    diff_lst = df['diff'].tolist()

    return list(zip(real_lst, fake1_lst, fake2_lst, same_lst, diff_lst))


def create_batch(lst):
    seqs = [sig.transpose(0, 1) for sig in lst]
    padded = pad_sequence(seqs, batch_first=True).squeeze(2)
    lens = torch.tensor([sig.shape[0] for sig in seqs])
    return padded, lens


class PairDataset(Dataset):
    def __init__(self, metadata_path: Path, sr_rate=CONFIG["global"]["sample_rate"]):
        self.pairs = load_data(metadata_path)
        self.sr_rate = sr_rate

    def __len__(self):
        return len(self.pairs)

    def __getitem__(self, idx):
        real_fname, fake1_fname, fake2_fname, same_fname, diff_fname = self.pairs[idx]

        return {
            "real": self.data_load_fn(real_fname),
            "fake1": self.data_load_fn(fake1_fname),
            "fake2": self.data_load_fn(fake2_fname),
            "same": self.data_load_fn(same_fname),
            "diff": self.data_load_fn(diff_fname),
            "real_spk": speaker2idx.get(real_fname.split("/")[-2]),
            "fake1_spk": -1,
            "fake2_spk": -1,
            "same_spk": speaker2idx.get(same_fname.split("/")[-2]),
            "diff_spk": speaker2idx.get(diff_fname.split("/")[-2]),
        }

    def data_load_fn(self, fp):
        waveform, sample_rate = torchaudio.load(fp)
        waveform = waveform.mean(dim=0, keepdim=True)
        if sample_rate != self.sr_rate:
            waveform = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=self.sr_rate)(waveform)
        return waveform


def collate_fn(batch):
    # turn list of dicts into dict of lists
    grouped = {k: [sample[k] for sample in batch] for k in batch[0].keys()}
    padded, lengths = {}, {}

    for k, seqs in grouped.items():
        # if this key is one of your speaker-ID fields, just turn it into a LongTensor
        if k.endswith("_spk"):
            padded[k] = torch.tensor(seqs, dtype=torch.long)
        else:
            padded[k], lengths[k] = create_batch(seqs)

    return padded, lengths

train_dataset = PairDataset(METADATA_PATH["dd_in_train"])
test_dataset = PairDataset(METADATA_PATH["dd_in_test"])
cross_dataset = PairDataset(METADATA_PATH["dd_out"])
train_loader = DataLoader(
    train_dataset,
    batch_size=6,
    shuffle=True,
    collate_fn=collate_fn,
    num_workers=4,
)
test_loader = DataLoader(
    test_dataset,
    batch_size=6,
    shuffle=False,
    collate_fn=collate_fn,
    num_workers=4,
)
cross_loader = DataLoader(
    cross_dataset,
    batch_size=6,
    shuffle=False,
    collate_fn=collate_fn,
    num_workers=4
)

import torch
from sklearn.metrics import (
    confusion_matrix,
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    classification_report
)


def test(data_loader):
    all_scores = []
    all_preds = []
    all_labels = []
    for batch in data_loader:
        padded, lens = batch
        real_wav, real_lens = padded["real"], lens["real"]
        fake1_wav, fake1_lens = padded["fake1"], lens["fake1"]
        fake2_wav, fake2_lens = padded["fake2"], lens["fake2"]
        same_wav, same_lens = padded["same"], lens["same"]
        diff_wav, diff_lens = padded["diff"], lens["diff"]
        score1, pred1 = verification.verify_batch(real_wav, fake1_wav, real_lens, fake1_lens)
        score2, pred2 = verification.verify_batch(real_wav, fake2_wav, real_lens, fake2_lens)
        score3, pred3 = verification.verify_batch(real_wav, same_wav, real_lens, same_lens)
        score4, pred4 = verification.verify_batch(real_wav, diff_wav, real_lens, diff_lens)

        batch_scores = torch.cat([score1, score2, score3, score4]).cpu().tolist()
        batch_preds = torch.cat([pred1, pred2, pred3, pred4]).cpu().tolist()
        batch_labels = (
                [0] * len(pred1) +
                [0] * len(pred2) +
                [1] * len(pred3) +
                [0] * len(pred4)
        )

        all_scores.extend(batch_scores)
        all_preds.extend(batch_preds)
        all_labels.extend(batch_labels)

    scores_arr = np.array(all_scores)
    labels_arr = np.array(all_labels)
    scores_flat = scores_arr.ravel()
    labels_flat = labels_arr.ravel()
    mask = np.isfinite(scores_flat)
    if not mask.all():
        n_bad = len(scores_flat) - mask.sum()
        print(f"Dropping {n_bad} NaN scores before AUC.")
    scores_clean = scores_flat[mask]
    labels_clean = labels_flat[mask]
    if len(scores_clean) > 0:
        auc = roc_auc_score(labels_clean, scores_clean)
    else:
        auc = float("nan")
        print("Warning: no valid scores to compute ROC-AUC.")

    cm = confusion_matrix(all_labels, all_preds)
    acc = accuracy_score(all_labels, all_preds)
    prec = precision_score(all_labels, all_preds)
    rec = recall_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds)
    report = classification_report(all_labels, all_preds)
    return {
        "confusion_matrix": cm.tolist(),
        "accuracy": acc,
        "precision": prec,
        "recall": rec,
        "f1_score": f1,
        "roc_auc": auc,
        "classification_report": report
    }

def evaluate():
    print("=== Evaluation Results ===")
    print("Train Dataset: ")
    for k, v in test(train_loader).items():
        print(f"{k}: \n{v}\n")

    print("Test Dataset: ")
    for k, v in test(test_loader).items():
        print(f"{k}: \n{v}\n")

    print("Cross Dataset: ")
    for k, v in test(cross_loader).items():
        print(f"{k}: \n{v}\n")

def test_encoder(loader):
    batch = next(iter(loader))
    padded, lens = batch
    real_wav, real_lens = padded["real"], lens["real"]
    fake1_wav, fake1_lens = padded["fake1"], lens["fake1"]
    fake2_wav, fake2_lens = padded["fake2"], lens["fake2"]
    same_wav, same_lens = padded["same"], lens["same"]
    diff_wav, diff_lens = padded["diff"], lens["diff"]

    embedding_real = classifier.encode_batch(real_wav, real_lens).squeeze(1)
    embedding_fake1 = classifier.encode_batch(fake1_wav, fake1_lens).squeeze(1)
    embedding_fake2 = classifier.encode_batch(fake2_wav, fake2_lens).squeeze(1)
    embedding_same = classifier.encode_batch(same_wav, same_lens).squeeze(1)
    embedding_diff = classifier.encode_batch(diff_wav, diff_lens).squeeze(1)

    sim_fake1 = F.cosine_similarity(embedding_real, embedding_fake1, dim=1)
    sim_fake2 = F.cosine_similarity(embedding_real, embedding_fake2, dim=1)
    sim_same = F.cosine_similarity(embedding_real, embedding_same, dim=1)
    sim_diff = F.cosine_similarity(embedding_real, embedding_diff, dim=1)

    score1, pred1 = verification.verify_batch(real_wav, fake1_wav, real_lens, fake1_lens)
    score2, pred2 = verification.verify_batch(real_wav, fake2_wav, real_lens, fake2_lens)
    score3, pred3 = verification.verify_batch(real_wav, same_wav, real_lens, same_lens)
    score4, pred4 = verification.verify_batch(real_wav, diff_wav, real_lens, diff_lens)

    print("Cosine similarities (per sample):")
    print(f"  -real vs. fake1: {sim_fake1}")
    print(f"  +real vs. fake1: {score1.flatten()}")
    print(f"  >Result: {pred1.tolist()}")
    print(f"  -real vs. fake2: {sim_fake2}")
    print(f"  +real vs. fake2: {score2.flatten()}")
    print(f"  >Result: {pred2.tolist()}")
    print(f"  -real vs. same : {sim_same}")
    print(f"  +real vs. same : {score3.flatten()}")
    print(f"  >Result: {pred3.tolist()}")
    print(f"  -real vs. diff : {sim_diff}")
    print(f"  +real vs. diff : {score4.flatten()}")
    print(f"  >Result: {pred4.tolist()}")

def test_spk(loader):
    batch = next(iter(loader))
    padded, lens = batch
    real_spk, fake1_spk, fake2_spk, same_spk, diff_spk = padded["real_spk"], padded["fake1_spk"], padded["fake2_spk"], padded["same_spk"], padded["diff_spk"]
    print(real_spk, fake1_spk, fake2_spk, same_spk, diff_spk)


# test_spk(test_loader)


def init_weights(module):
    if isinstance(module, nn.Linear):
        nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
        if module.bias is not None:
            nn.init.zeros_(module.bias)
    elif isinstance(module, nn.BatchNorm1d):
        nn.init.ones_(module.weight)
        nn.init.zeros_(module.bias)

class FeatureExtractor(nn.Module):
    def __init__(
            self,
            backbone: nn.Module,
            adapter_dim: int = 256,
            unfreeze_backbone_layers: int = 0,
            base_hidden: int = 192,
    ):
        super().__init__()
        self.backbone = backbone

        self.adapter = nn.Sequential(
            nn.Linear(base_hidden, adapter_dim * 2, bias=False),
            nn.BatchNorm1d(adapter_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(adapter_dim * 2, adapter_dim, bias=False),
            nn.BatchNorm1d(adapter_dim),
            nn.GELU(),
        )
        self.adapter.apply(init_weights)

    def forward(self, x: torch.Tensor, wavlen: Optional[torch.Tensor] = None) -> torch.Tensor:
        with torch.no_grad():
            out = self.backbone.encode_batch(x, wavlen)
            if torch.isnan(out).any() or torch.isinf(out).any():
                out = torch.nan_to_num(out, nan=0.0, posinf=1e6, neginf=-1e6)
                # print(f"Backbone output: {out}")
                # print(f"x: {x}")
                # print(f"wavlen: {wavlen}")
                # raise ValueError("Backbone returns NaN or Inf!")
        out = out.squeeze(1)
        adapted = self.adapter(out)
        adapted = F.normalize(adapted, p=2, dim=1, eps=1e-6)
        return adapted


feature_extractor = FeatureExtractor(backbone=classifier).to(device)

fe_loss = losses.TripletMarginLoss(
    margin=CONFIG["train"]['loss_margin']
).to(device)

fe_miner = miners.TripletMarginMiner(
    margin=CONFIG["train"]['loss_margin'],
    type_of_triplets="hard"
).to(device)

fe_optimizer = optim.AdamW([
    {'params': feature_extractor.adapter.parameters(), 'lr': CONFIG["train"]["opt_lr"]},
    {'params': feature_extractor.backbone.parameters(), 'lr': CONFIG["train"]["backbone_lr"]}
], weight_decay=CONFIG["train"]["opt_weight_decay"])

fe_schedular = ReduceLROnPlateau(fe_optimizer,
                                 mode='min',
                                 factor=CONFIG['train']['lr_scheduler']['factor'],
                                 patience=CONFIG['train']['lr_scheduler']['patience'],
                                 threshold=CONFIG['train']['lr_scheduler']['threshold'],
                                 threshold_mode=CONFIG['train']['lr_scheduler']['threshold_mode'])


# Evaluation Functions
def evaluate_similarity(real, coms, threshold=0.25):
    """
    real_emb: the real embedding
    com_embs: the embeddings to compare to, incl. [(emb & lbl)...]
    """
    real_lbls = []
    pred_lbls = []

    sims = torch.stack(
        [F.cosine_similarity(real, emb, dim=1) for emb, _ in coms],
        dim=1
    )
    batch_size = real.size(0)

    for group_idx, (_, lbl) in enumerate(coms):
        real_lbls.extend([lbl] * batch_size)
        preds = (sims[:, group_idx] >= threshold)
        pred_lbls.extend(preds.tolist())

    return real_lbls, pred_lbls, sims.cpu().flatten().tolist()


# Test model
def test_model():
    batch = next(iter(train_loader))
    padded, lens = batch
    real_wav, real_lens = padded["real"], lens["real"]
    fake1_wav, fake1_lens = padded["fake1"], lens["fake1"]
    fake2_wav, fake2_lens = padded["fake2"], lens["fake2"]
    same_wav, same_lens = padded["same"], lens["same"]
    diff_wav, diff_lens = padded["diff"], lens["diff"]

    real_emb = feature_extractor(real_wav, real_lens)
    fake1_emb = feature_extractor(fake1_wav, fake1_lens)
    fake2_emb = feature_extractor(fake2_wav, fake2_lens)
    same_emb = feature_extractor(same_wav, same_lens)
    diff_emb = feature_extractor(diff_wav, diff_lens)

    real_spk, fake1_spk, fake2_spk, same_spk, diff_spk = padded["real_spk"], padded["fake1_spk"], padded["fake2_spk"], \
        padded["same_spk"], padded["diff_spk"]

    embeddings = torch.cat(
        [real_emb, same_emb, fake1_emb, fake2_emb, diff_emb],
        dim=0
    )
    labels = torch.cat(
        [real_spk, same_spk, fake1_spk, fake2_spk, diff_spk],
        dim=0
    ).long()

    hard_triplets = fe_miner(embeddings, labels)
    loss = fe_loss(embeddings, labels, hard_triplets)

    sim_diff = F.cosine_similarity(real_emb, diff_emb, dim=1)
    sim_same = F.cosine_similarity(real_emb, same_emb, dim=1)

    rl, pl, sc = evaluate_similarity(real_emb, [(fake1_emb, False), (fake2_emb, False), (same_emb, True), (diff_emb, False)])

    print(
        f"""
            =======================
            INFO:
            REAL: {real_emb}
            LABEL: {real_spk}
            -----------------------
            PREDICTION:
            GT: {rl}
            PRED: {pl}
            SCORE: {sc}
            =======================
            Test Result:
            =======================
            loss:
            {loss}
            =======================
            similarity:
            {sim_diff.cpu().tolist()}
            {sim_same.cpu().tolist()}
            =======================
        """
    )

test_model()

def _prepare_batch(batch, device):
    """
    Move tensors in padded and lens to device and extract embeddings and labels.
    Returns embeddings (Tensor), labels (LongTensor), and a dict of individual embeddings.
    """
    padded, lens = batch
    padded = {k: v.to(device) for k, v in padded.items()}
    lens = {k: v.to(device) for k, v in lens.items()}

    # Keys defining the order
    keys = ["real", "same", "fake1", "fake2", "diff"]

    # Extract embeddings per key
    embeddings_list = []
    labels_list = []
    embeddings_dict = {}
    for key in keys:
        emb = feature_extractor(padded[key], lens[key])
        embeddings_list.append(emb)
        labels_list.append(padded[f"{key}_spk"])
        embeddings_dict[key] = emb

    # Concatenate for loss computation
    embeddings = torch.cat(embeddings_list, dim=0)
    labels = torch.cat(labels_list, dim=0).long()

    return embeddings, labels, embeddings_dict

def train_one_epoch(train_loader, feature_extractor, fe_miner, fe_loss, fe_optimizer, device):
    feature_extractor.train()
    total_loss = 0.0

    loader = tqdm(train_loader, desc="Train", unit="batch")
    for batch_idx, batch in enumerate(loader, 1):
        embeddings, labels, _ = _prepare_batch(batch, device)

        if torch.isnan(embeddings).any():
            raise ValueError("NaNs detected in embeddings")

        # Mining and loss
        hard_triplets = fe_miner(embeddings, labels)
        loss = fe_loss(embeddings, labels, hard_triplets)

        # Backpropagation
        fe_optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(feature_extractor.parameters(), max_norm=5.0)
        fe_optimizer.step()

        # Metrics
        batch_loss = loss.item()
        total_loss += batch_loss
        avg_loss = total_loss / batch_idx
        loader.set_postfix(batch_loss=f"{batch_loss:.4f}", avg_loss=f"{avg_loss:.4f}")

    epoch_avg = total_loss / len(train_loader)
    print(f"Epoch finished — Total loss: {total_loss:.4f}, Average loss: {epoch_avg:.4f}")

def evaluate_one_epoch(loader, feature_extractor, fe_miner, fe_loss, device, desc="Eval"):
    feature_extractor.eval()
    total_loss = 0.0
    correct = 0
    total = 0

    loader = tqdm(loader, desc=desc, unit="batch")
    with torch.no_grad():
        for batch_idx, batch in enumerate(loader, 1):
            embeddings, labels, embs = _prepare_batch(batch, device)

            # Loss
            hard_triplets = fe_miner(embeddings, labels)
            loss = fe_loss(embeddings, labels, hard_triplets)
            total_loss += loss.item()

            # Similarity evaluation
            real_emb = embs['real']
            sim_inputs = [
                (embs['fake1'], False),
                (embs['fake2'], False),
                (embs['same'], True),
                (embs['diff'], False)
            ]
            real_lbls, pred_lbls, _ = evaluate_similarity(real_emb, sim_inputs)
            batch_correct = sum(gt == pred for gt, pred in zip(real_lbls, pred_lbls))
            correct += batch_correct
            total += len(real_lbls)

            # Metrics
            avg_loss = total_loss / batch_idx
            acc = correct / total if total > 0 else 0.0
            weighted_acc = acc * (batch_idx / len(loader))
            loader.set_postfix(
                batch_loss=f"{loss.item():.4f}",
                avg_loss=f"{avg_loss:.4f}",
                acc=f"{acc:.4f}",
                weighted_acc=f"{weighted_acc:.4f}"
            )

    epoch_avg_loss = total_loss / len(loader)
    epoch_acc = correct / total if total > 0 else 0.0
    print(f"[{desc}] Loss: {epoch_avg_loss:.4f} | Accuracy: {epoch_acc:.4f}")
    return epoch_avg_loss, epoch_acc

def train():
    best_val_loss = float('inf')
    best_cross_loss = float('inf')
    for epoch in range(1, CONFIG['train']['num_epochs'] + 1):
        print(f"\n=== Epoch {epoch}/{CONFIG['train']['num_epochs'] + 1} ===")
        train_one_epoch(train_loader, feature_extractor, fe_miner, fe_loss, fe_optimizer, device)
        val_loss, val_acc = evaluate_one_epoch(test_loader, feature_extractor, fe_miner, fe_loss, device, desc="Eval")
        if CHECKPOINT_PATH.exists() and val_loss < best_val_loss:
            best_val_loss = val_loss
            path = CHECKPOINT_PATH / f"best_val_epoch_{epoch}.pt"
            torch.save(feature_extractor.state_dict(), path)
            print(f"Saved new best model to {path}")
        if epoch % 5 == 0:
            cross_loss, cross_acc = evaluate_one_epoch(cross_loader, feature_extractor, fe_miner, fe_loss, device, desc="Eval")
            if CHECKPOINT_PATH.exists() and cross_loss < best_cross_loss:
                best_cross_loss = cross_loss
                path = CHECKPOINT_PATH / f"best_cross_epoch_{epoch}.pt"
                torch.save(feature_extractor.state_dict(), path)
                print(f"Saved new best cross-dataset model to {path}")
train()

